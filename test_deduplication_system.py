#!/usr/bin/env python3
"""
Test script to demonstrate the comprehensive deduplication system
"""

import os
import sys
import json
from typing import List, Dict

# Add src/core to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'core'))

def create_test_findings() -> List[Dict]:
    """Create test findings with various types of duplicates"""
    
    findings = [
        # Exact duplicates (same control_id, file, line, description)
        {
            "control_id": "NS-1",
            "file_path": "storage.json",
            "line": 25,
            "severity": "HIGH",
            "description": "Storage account allows public access",
            "remediation": "Set allowBlobPublicAccess to false"
        },
        {
            "control_id": "NS-1", 
            "file_path": "storage.json",
            "line": 25,
            "severity": "HIGH",
            "description": "Storage account allows public access",
            "remediation": "Set allowBlobPublicAccess to false"
        },
        
        # Semantic duplicates (same control, similar descriptions)
        {
            "control_id": "DP-3",
            "file_path": "keyvault.json",
            "line": 15,
            "severity": "MEDIUM",
            "description": "Key Vault does not have encryption enabled",
            "remediation": "Enable encryption for Key Vault"
        },
        {
            "control_id": "DP-3",
            "file_path": "keyvault.json", 
            "line": 18,
            "severity": "MEDIUM",
            "description": "Key Vault lacks proper encryption configuration",
            "remediation": "Configure encryption settings for Key Vault"
        },
        
        # Content-based duplicates (same control, nearby lines, same resource)
        {
            "control_id": "IM-1",
            "file_path": "webapp.json",
            "line": 30,
            "severity": "HIGH",
            "description": "Web app \"myapp\" missing authentication configuration",
            "remediation": "Configure authentication for web app"
        },
        {
            "control_id": "IM-1",
            "file_path": "webapp.json",
            "line": 32,
            "severity": "HIGH", 
            "description": "Web app \"myapp\" requires authentication setup",
            "remediation": "Set up authentication for web app"
        },
        
        # Cross-file pattern duplicates (same issue across different files)
        {
            "control_id": "NS-2",
            "file_path": "template1.json",
            "line": 40,
            "severity": "CRITICAL",
            "description": "Network security group allows unrestricted inbound access",
            "remediation": "Restrict NSG rules to specific ports and sources"
        },
        {
            "control_id": "NS-2",
            "file_path": "template2.json",
            "line": 45,
            "severity": "CRITICAL",
            "description": "Network security group allows unrestricted inbound access", 
            "remediation": "Restrict NSG rules to specific ports and sources"
        },
        
        # Different severity - should be kept
        {
            "control_id": "NS-2",
            "file_path": "template3.json",
            "line": 50,
            "severity": "HIGH",  # Different severity
            "description": "Network security group allows unrestricted inbound access",
            "remediation": "Restrict NSG rules to specific ports and sources"
        },
        
        # Unique findings (should be preserved)
        {
            "control_id": "DP-1",
            "file_path": "database.json",
            "line": 60,
            "severity": "HIGH",
            "description": "Database encryption at rest not enabled",
            "remediation": "Enable transparent data encryption"
        },
        {
            "control_id": "IM-3",
            "file_path": "identity.json", 
            "line": 70,
            "severity": "MEDIUM",
            "description": "Role assignments too permissive",
            "remediation": "Apply principle of least privilege"
        }
    ]
    
    return findings

def test_deduplication():
    """Test the comprehensive deduplication system"""
    
    print("🧪 Testing Comprehensive Deduplication System")
    print("=" * 60)
    
    # Create test findings
    test_findings = create_test_findings()
    print(f"📊 Created {len(test_findings)} test findings with various duplicate types")
    
    # Import the security reviewer
    try:
        from security_opt import SecurityPRReviewer
        
        # Create reviewer instance with local mode
        reviewer = SecurityPRReviewer(local_folder="test")
        
        # Test comprehensive deduplication
        print(f"\n🔍 Testing comprehensive deduplication...")
        deduplicated = reviewer._comprehensive_deduplicate_findings(test_findings, "test")
        
        print(f"\n📈 Deduplication Results:")
        print(f"   • Original findings: {len(test_findings)}")
        print(f"   • After deduplication: {len(deduplicated)}")
        print(f"   • Duplicates removed: {len(test_findings) - len(deduplicated)}")
        print(f"   • Reduction: {((len(test_findings) - len(deduplicated)) / len(test_findings) * 100):.1f}%")
        
        # Analyze what was kept
        print(f"\n📋 Remaining Findings by Control ID:")
        control_counts = {}
        for finding in deduplicated:
            control_id = finding.get("control_id", "UNKNOWN")
            if control_id not in control_counts:
                control_counts[control_id] = 0
            control_counts[control_id] += 1
        
        for control_id, count in sorted(control_counts.items()):
            print(f"   • {control_id}: {count} finding(s)")
        
        # Show detailed results
        print(f"\n📝 Detailed Remaining Findings:")
        for i, finding in enumerate(deduplicated, 1):
            file_name = finding.get("file_path", "").split("/")[-1]
            print(f"   {i}. {finding.get('control_id')} - {file_name}:{finding.get('line')} - {finding.get('severity')}")
            if finding.get("related_files"):
                print(f"      Related files: {', '.join([f.split('/')[-1] for f in finding['related_files']])}")
            if finding.get("primary_occurrence"):
                print(f"      Primary occurrence: {finding['primary_occurrence'].split('/')[-1]}")
        
        # Test individual deduplication strategies
        print(f"\n🔬 Testing Individual Deduplication Strategies:")
        
        # Test exact match deduplication
        exact_deduplicated = reviewer._exact_match_deduplication(test_findings)
        print(f"   • Exact match: {len(test_findings)} → {len(exact_deduplicated)} (-{len(test_findings) - len(exact_deduplicated)})")
        
        # Test semantic deduplication
        semantic_deduplicated = reviewer._semantic_deduplication(exact_deduplicated)
        print(f"   • Semantic: {len(exact_deduplicated)} → {len(semantic_deduplicated)} (-{len(exact_deduplicated) - len(semantic_deduplicated)})")
        
        # Test content-based deduplication
        content_deduplicated = reviewer._content_based_deduplication(semantic_deduplicated)
        print(f"   • Content-based: {len(semantic_deduplicated)} → {len(content_deduplicated)} (-{len(semantic_deduplicated) - len(content_deduplicated)})")
        
        # Test cross-file pattern deduplication
        final_deduplicated = reviewer._cross_file_pattern_deduplication(content_deduplicated)
        print(f"   • Cross-file pattern: {len(content_deduplicated)} → {len(final_deduplicated)} (-{len(content_deduplicated) - len(final_deduplicated)})")
        
        print(f"\n✅ Comprehensive deduplication system is working correctly!")
        print(f"🎯 The system successfully prevents duplicate recommendations while preserving unique findings.")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import SecurityPRReviewer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing deduplication: {e}")
        return False

if __name__ == "__main__":
    success = test_deduplication()
    if success:
        print(f"\n🎉 All deduplication tests passed!")
    else:
        print(f"\n💥 Some tests failed!")
        sys.exit(1)
